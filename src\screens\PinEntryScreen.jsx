import React, { useState, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useApp } from '../context/AppContext'
import { customApi } from '../services/customApi'
import { InlineNumericKeyboard } from '../components/OnScreenKeyboard'
import { useInlineKeyboard } from '../hooks/useInlineKeyboard'

const PinEntryScreen = () => {
  const navigate = useNavigate()
  const { state, actions } = useApp()
  const [pin, setPin] = useState('')
  const pinInputRef = useRef(null)

  // Initialize inline keyboard
  const {
    isKeyboardVisible,
    isKeyboardEnabled,
    keyboardProps,
    handleInputFocus
  } = useInlineKeyboard(pinInputRef, {
    type: 'numeric',
    showEnter: true,
    onEnter: () => {
      if (pin.length >= 4) {
        handleSubmit(new Event('submit'))
      }
    },
    onKeyPress: (key, newValue) => {
      // Update state when keyboard is used
      const numericValue = newValue.replace(/\D/g, '')
      if (numericValue.length <= 6) {
        setPin(numericValue)
      }
    }
  })

  const handlePinChange = (e) => {
    const value = e.target.value.replace(/\D/g, '') // Only allow digits
    if (value.length <= 6) { // Limit to 6 digits
      setPin(value)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!pin.trim() || pin.length < 4) {
      actions.setError('Please enter a valid PIN (at least 4 digits)')
      return
    }

    actions.setLoading(true)
    actions.clearError()

    try {
      const result = await customApi.loginWithPin(pin)

      if (result.success) {
        actions.setUser(result.user)
        // Always go to practice selection for PIN users to give them choice
        navigate('/practice-selection')
      } else {
        actions.setError(result.error)
      }
    } catch (error) {
      console.error('PIN login error:', error)
      actions.setError('Login failed. Please try again.')
    } finally {
      actions.setLoading(false)
    }
  }

  const handleBack = () => {
    navigate('/')
  }



  return (
    <div className="container flex-center">
      <motion.div
        className="card"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
        style={{ maxWidth: '800px', width: '100%' }}
      >
        <motion.h1
          className="title"
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          Enter Your PIN
        </motion.h1>



        {state.error && (
          <motion.div 
            className="alert alert-error"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {state.error}
          </motion.div>
        )}

        <form onSubmit={handleSubmit} className="flex-column gap-lg">
          <motion.div
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            <label
              htmlFor="pin"
              className="text-lg mb-sm"
              style={{
                display: 'block',
                fontWeight: '700',
                color: 'var(--text-secondary)',
                textAlign: 'center'
              }}
            >
              PIN
            </label>
            <div style={{
              position: 'relative',
              display: 'flex',
              justifyContent: 'center',
              width: '100%'
            }}>
              <input
                type="text"
                id="pin"
                name="pin"
                value={pin}
                onChange={handlePinChange}
                onFocus={handleInputFocus}
                className="input pin-input"
                placeholder="Enter PIN"
                maxLength="6"
                autoComplete="off"
                ref={pinInputRef}
                style={{
                  fontSize: 'clamp(var(--font-size-lg), 5vw, var(--font-size-xxl))',
                  padding: 'clamp(var(--spacing-md), 3vw, var(--spacing-lg))',
                  border: '3px solid var(--primary-light)',
                  borderRadius: 'var(--radius-md)',
                  textAlign: 'center',
                  fontWeight: '700',
                  letterSpacing: 'clamp(4px, 2vw, 12px)',
                  minHeight: 'clamp(60px, 12vw, 80px)',
                  width: '100%',
                  maxWidth: '650px',
                  margin: '0 auto'
                }}
              />
            </div>
          </motion.div>



          <div className="flex-column gap-md mt-lg">
            <motion.button
              type="submit"
              className="btn btn-primary btn-large"
              disabled={state.isLoading || pin.length < 4}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              whileHover={{ scale: (state.isLoading || pin.length < 4) ? 1 : 1.02 }}
              whileTap={{ scale: (state.isLoading || pin.length < 4) ? 1 : 0.98 }}
              style={{
                background: (state.isLoading || pin.length < 4)
                  ? 'var(--primary-light)'
                  : 'linear-gradient(135deg, var(--accent) 0%, #ff8f00 100%)',
                opacity: (state.isLoading || pin.length < 4) ? 0.7 : 1,
                cursor: (state.isLoading || pin.length < 4) ? 'not-allowed' : 'pointer'
              }}
            >
              {state.isLoading ? (
                <div className="flex-center gap-md">
                  <div className="spinner" style={{ width: '24px', height: '24px' }} />
                  Logging in...
                </div>
              ) : (
                '🔐 Login'
              )}
            </motion.button>

            <motion.button
              type="button"
              className="btn btn-secondary"
              onClick={handleBack}
              disabled={state.isLoading}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              whileHover={{ scale: state.isLoading ? 1 : 1.02 }}
              whileTap={{ scale: state.isLoading ? 1 : 0.98 }}
              style={{
                opacity: state.isLoading ? 0.5 : 1,
                cursor: state.isLoading ? 'not-allowed' : 'pointer'
              }}
            >
              ← Back to Login Options
            </motion.button>
          </div>
        </form>

     

        {/* Inline Numeric Keyboard */}
        {isKeyboardEnabled && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.5 }}
            style={{
              marginTop: 'clamp(16px, 3vw, 24px)',
              width: '100%',
              maxWidth: '100%',
              padding: '0 clamp(16px, 4vw, 32px)',
              boxSizing: 'border-box'
            }}
          >
            <InlineNumericKeyboard
              {...keyboardProps}
            />
          </motion.div>
        )}
      </motion.div>
    </div>
  )
}

export default PinEntryScreen
