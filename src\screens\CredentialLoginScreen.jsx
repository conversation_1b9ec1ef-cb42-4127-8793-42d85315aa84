import React, { useState, useRef } from 'react'
import { useNavigate } from 'react-router-dom'
import { motion } from 'framer-motion'
import { useApp } from '../context/AppContext'
import { customApi } from '../services/customApi'
import { TextKeyboard } from '../components/OnScreenKeyboard'
import { useOnScreenKeyboard } from '../hooks/useOnScreenKeyboard'

const CredentialLoginScreen = () => {
  const navigate = useNavigate()
  const { state, actions } = useApp()
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  })
  const [showPassword, setShowPassword] = useState(false)

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!formData.username.trim() || !formData.password.trim()) {
      actions.setError('Please enter both username and password')
      return
    }

    actions.setLoading(true)
    actions.clearError()

    try {
      const result = await customApi.loginWithCredentials(formData.username, formData.password)

      if (result.success) {
        actions.setUser(result.user)
        console.log('Login successful:', result.message)
        navigate('/practice-selection')
      } else {
        actions.setError(result.error)
      }
    } catch (error) {
      console.error('Login error:', error)
      actions.setError('Login failed. Please try again.')
    } finally {
      actions.setLoading(false)
    }
  }

  const handleBack = () => {
    navigate('/')
  }

  return (
    <div className="container flex-center">
      <motion.div
        className="card"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
        style={{ maxWidth: '800px', width: '100%' }}
      >
        <motion.h1
          className="title"
          initial={{ y: -20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          Patient Login
        </motion.h1>

        {state.error && (
          <motion.div 
            className="alert alert-error"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {state.error}
          </motion.div>
        )}

        <form onSubmit={handleSubmit} className="flex-column gap-lg">
          <motion.div
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            <label 
              htmlFor="username" 
              className="text-lg mb-sm"
              style={{ 
                display: 'block', 
                fontWeight: '700',
                color: 'var(--text-secondary)'
              }}
            >
              Username
            </label>
            <input
              type="text"
              id="username"
              name="username"
              value={formData.username}
              onChange={handleInputChange}
              className="input"
              placeholder="Enter your username"
              autoComplete="username"
              style={{
                fontSize: 'var(--font-size-lg)',
                padding: 'var(--spacing-lg)',
                border: '3px solid var(--primary-light)',
                borderRadius: 'var(--radius-md)'
              }}
            />
          </motion.div>

          <motion.div
            initial={{ x: -20, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            <label 
              htmlFor="password" 
              className="text-lg mb-sm"
              style={{ 
                display: 'block', 
                fontWeight: '700',
                color: 'var(--text-secondary)'
              }}
            >
              Password
            </label>
            <div style={{ position: 'relative' }}>
              <input
                type={showPassword ? "text" : "password"}
                id="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                className="input"
                placeholder="Enter your password"
                autoComplete="current-password"
                style={{
                  fontSize: 'var(--font-size-lg)',
                  padding: 'var(--spacing-lg)',
                  paddingRight: '80px',
                  border: '3px solid var(--primary-light)',
                  borderRadius: 'var(--radius-md)'
                }}
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                style={{
                  position: 'absolute',
                  right: '16px',
                  top: '50%',
                  transform: 'translateY(-50%)',
                  background: 'none',
                  border: 'none',
                  fontSize: 'var(--font-size-lg)',
                  cursor: 'pointer',
                  color: 'var(--text-secondary)',
                  padding: '8px'
                }}
              >
                {showPassword ? '🙈' : '👁️'}
              </button>
            </div>
          </motion.div>

          <div className="flex-column gap-md mt-lg">
            <motion.button
              type="submit"
              className="btn btn-primary btn-large"
              disabled={state.isLoading}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.5, duration: 0.5 }}
              whileHover={{ scale: state.isLoading ? 1 : 1.02 }}
              whileTap={{ scale: state.isLoading ? 1 : 0.98 }}
              style={{
                background: state.isLoading 
                  ? 'var(--primary-light)' 
                  : 'linear-gradient(135deg, var(--accent) 0%, #ff8f00 100%)',
                opacity: state.isLoading ? 0.7 : 1,
                cursor: state.isLoading ? 'not-allowed' : 'pointer'
              }}
            >
              {state.isLoading ? (
                <div className="flex-center gap-md">
                  <div className="spinner" style={{ width: '24px', height: '24px' }} />
                  Logging in...
                </div>
              ) : (
                '🔐 Login'
              )}
            </motion.button>

            <motion.button
              type="button"
              className="btn btn-secondary"
              onClick={handleBack}
              disabled={state.isLoading}
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.6, duration: 0.5 }}
              whileHover={{ scale: state.isLoading ? 1 : 1.02 }}
              whileTap={{ scale: state.isLoading ? 1 : 0.98 }}
              style={{
                opacity: state.isLoading ? 0.5 : 1,
                cursor: state.isLoading ? 'not-allowed' : 'pointer'
              }}
            >
              ← Back to Login Options
            </motion.button>
          </div>
        </form>

        <motion.div 
          className="text-center mt-lg"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.5 }}
        >
          <p style={{ 
            fontSize: 'var(--font-size-sm)', 
            color: 'var(--text-secondary)', 
            opacity: 0.6,
            fontWeight: '500'
          }}>
            Demo credentials: admin/admin123 or staff/staff123
          </p>
        </motion.div>
      </motion.div>
    </div>
  )
}

export default CredentialLoginScreen
