// Custom API service for migrating from Supabase
// Base URL for all API endpoints
const API_BASE_URL = 'https://localhost:44356/api'

// Utility function to simulate API delay for better UX
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms))

// HTTP request helper with error handling
const apiRequest = async (endpoint, options = {}) => {
  try {
    const url = `${API_BASE_URL}${endpoint}`
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    }

    console.log(`🌐 API Request: ${config.method || 'GET'} ${url}`)
    if (config.body) {
      console.log('📤 Request Body:', JSON.parse(config.body))
    }

    const response = await fetch(url, config)

    // Handle different response types
    let data
    const contentType = response.headers.get('content-type')
    if (contentType && contentType.includes('application/json')) {
      data = await response.json()
    } else {
      const text = await response.text()
      console.warn('⚠️ Non-JSON response received:', text)
      data = { message: text }
    }

    console.log(`📥 API Response (${response.status}):`, data)

    return {
      ok: response.ok,
      status: response.status,
      data
    }
  } catch (error) {
    console.error('❌ API Request Error:', error)

    // Handle network errors specifically
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return {
        ok: false,
        status: 0,
        error: 'Unable to connect to the server. Please check your network connection.'
      }
    }

    return {
      ok: false,
      status: 0,
      error: error.message || 'Network error occurred'
    }
  }
}

// Custom API service
export const customApi = {
  // Authentication APIs
  async loginWithCredentials(username, password) {
    await delay(1000) // Maintain UX delay
    
    try {
      const response = await apiRequest('/auth/LoginWithCredentials', {
        method: 'POST',
        body: JSON.stringify({
          userName: username,
          password: password
        })
      })

      if (!response.ok) {
        return { 
          success: false, 
          error: response.data?.message || 'Login failed' 
        }
      }

      const { profileID, userName, message } = response.data

      // Check if login was successful based on profileID
      if (profileID === 0 || !profileID) {
        return { 
          success: false, 
          error: message || 'Invalid username or password' 
        }
      }

      // Successful login
      return { 
        success: true, 
        user: {
          id: profileID,
          username: userName,
          role: 'admin' // Default role, can be adjusted based on your needs
        },
        message
      }
    } catch (error) {
      console.error('Login error:', error)
      return { 
        success: false, 
        error: 'Authentication failed. Please try again.' 
      }
    }
  },

  // PIN login (keeping existing logic for now, will need custom API later)
  async loginWithPin(pin) {
    await delay(800)
    
    // TODO: Replace with custom API when PIN endpoint is available
    // For now, using mock logic similar to Supabase version
    try {
      // Mock PIN validation - replace with actual API call
      const validPins = ['1234', '5678', '9999'] // This should come from your API
      
      if (!validPins.includes(pin)) {
        return { 
          success: false, 
          error: 'Invalid PIN. Please check your PIN and try again.' 
        }
      }
      
      return {
        success: true,
        user: { 
          id: 'kiosk', 
          username: 'kiosk', 
          role: 'kiosk' 
        }
      }
    } catch (error) {
      console.error('PIN validation error:', error)
      return { 
        success: false, 
        error: 'PIN validation failed' 
      }
    }
  },

  // Patient lookup APIs (placeholders for future implementation)
  async findPatientByDOB(dateOfBirth) {
    // TODO: Implement with custom API
    console.log('findPatientByDOB - Custom API not yet implemented')
    return { success: false, error: 'Custom API not yet implemented' }
  },

  async findAppointmentsByPatient(patientId, selectedDate = null) {
    // TODO: Implement with custom API
    console.log('findAppointmentsByPatient - Custom API not yet implemented')
    return { success: false, error: 'Custom API not yet implemented' }
  },

  async checkPatientBalance(patientId, practiceId) {
    // TODO: Implement with custom API
    console.log('checkPatientBalance - Custom API not yet implemented')
    return { success: false, error: 'Custom API not yet implemented' }
  },

  async completeCheckin(patientId, appointmentId) {
    // TODO: Implement with custom API
    console.log('completeCheckin - Custom API not yet implemented')
    return { success: false, error: 'Custom API not yet implemented' }
  },

  // Practice and location APIs (placeholders)
  async getPractices() {
    // TODO: Implement with custom API
    console.log('getPractices - Custom API not yet implemented')
    return { success: false, error: 'Custom API not yet implemented' }
  },

  async getLocationsByPractice(practiceId) {
    // TODO: Implement with custom API
    console.log('getLocationsByPractice - Custom API not yet implemented')
    return { success: false, error: 'Custom API not yet implemented' }
  },

  // Utility functions (keeping existing logic)
  getFormattedAddress(address) {
    if (!address) return 'Address not available'
    
    if (typeof address === 'string') {
      return address
    }
    
    const parts = []
    if (address.street) parts.push(address.street)
    if (address.city) parts.push(address.city)
    if (address.state) parts.push(address.state)
    if (address.zipCode) parts.push(address.zipCode)
    
    return parts.length > 0 ? parts.join(', ') : 'Address not available'
  }
}

export default customApi
